import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useMediaDeviceSelect } from "@livekit/components-react";
import { Dropdown } from "antd";
import TestButton from './TestButton';
import MicLevelIndicator from './MicLevelIndicator';
import VolumeControl from './VolumeControl';
import { ReactComponent as SpeakerIcon } from "../../Assets/SpeakerIcon.svg";
import soundTestAudio from "../../Assets/soundtest.mp3";

/**
 * SpeakerSettings Component
 * Complete speaker configuration section using modern LiveKit approach
 * @param {number} outputVolume - Current output volume (0-100)
 * @param {function} onOutputVolumeChange - Output volume change handler
 */
function SpeakerSettings({
  outputVolume = 100,
  onOutputVolumeChange
}) {
  // Use LiveKit's useMediaDeviceSelect hook for speaker devices
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({
    kind: "audiooutput"
  });

  // Local state for speaker testing
  const [isSpeakerTesting, setIsSpeakerTesting] = useState(false);
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);
  const testAudioRef = useRef(null);
  const levelIntervalRef = useRef(null);
  // Handle speaker device change using LiveKit's setActiveMediaDevice
  const handleSpeakerDeviceChange = useCallback(async (deviceId) => {
    try {
      await setActiveMediaDevice(deviceId);
      console.log('✅ Speaker device changed to:', deviceId);
    } catch (error) {
      console.error('❌ Failed to switch speaker device:', error);
    }
  }, [setActiveMediaDevice]);

  // Speaker dropdown content
  const speakerDropdownContent = useMemo(() => (
    <div className="speaker-device-dropdown-menu">
      {devices.map((device) => {
        const isSelected = activeDeviceId === device.deviceId;
        const deviceLabel = device.label || `Speaker ${device.deviceId.slice(0, 8)}...`;

        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleSpeakerDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [devices, activeDeviceId, handleSpeakerDeviceChange]);

  // Current device name for display
  const currentDeviceName = useMemo(() => {
    const currentDevice = devices.find(device => device.deviceId === activeDeviceId);
    return currentDevice?.label || 'Default Speaker';
  }, [devices, activeDeviceId]);

  // Test speaker functionality
  const testSpeaker = useCallback(async () => {
    if (isSpeakerTesting) {
      // Stop testing
      if (testAudioRef.current) {
        testAudioRef.current.pause();
        testAudioRef.current = null;
      }
      if (levelIntervalRef.current) {
        clearInterval(levelIntervalRef.current);
        levelIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      return;
    }

    // Start testing
    setIsSpeakerTesting(true);
    console.log('🔊 Starting speaker test with device:', activeDeviceId);

    try {
      // Create test audio element
      const audio = new Audio(soundTestAudio);
      testAudioRef.current = audio;
      audio.volume = outputVolume / 100;

      // Set the audio output device if supported
      if (activeDeviceId && activeDeviceId !== 'default' && audio.setSinkId) {
        try {
          await audio.setSinkId(activeDeviceId);
          console.log('✅ Successfully set speaker device to:', activeDeviceId);
        } catch (error) {
          console.warn('❌ Failed to set audio output device:', error);
        }
      }

      // Start level simulation
      levelIntervalRef.current = setInterval(() => {
        const level = Math.random() * 80 + 20; // Random level between 20-100
        setSpeakerAudioLevel(level);
      }, 100);

      // Play the audio
      await audio.play();

      // Stop simulation when audio ends
      audio.addEventListener('ended', () => {
        if (levelIntervalRef.current) {
          clearInterval(levelIntervalRef.current);
          levelIntervalRef.current = null;
        }
        setSpeakerAudioLevel(0);
        setIsSpeakerTesting(false);
        if (testAudioRef.current) {
          testAudioRef.current = null;
        }
      });

    } catch (error) {
      console.error('Error playing test sound:', error);
      // Cleanup on error
      if (levelIntervalRef.current) {
        clearInterval(levelIntervalRef.current);
        levelIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      if (testAudioRef.current) {
        testAudioRef.current = null;
      }
    }
  }, [isSpeakerTesting, activeDeviceId, outputVolume]);

  return (
    <div className="settings-section speaker-settings-section">
      <div className="grid-container speaker-grid">
        <div className="grid-cell left">
          <span className="setting-label">Speaker</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Speaker</span>
        </div>
        <div className="grid-cell center">
          <SpeakerDeviceDropdown
            devices={speakerDevices}
            selectedDeviceId={speakerDeviceId}
            onDeviceChange={onSpeakerDeviceChange}
            hasPermission={permissions.microphone}
            permissionMessage="Grant microphone permission to see devices"
            noDevicesMessage="No speaker devices found"
            defaultDeviceName="Default Speaker"
            selectPlaceholder="Select speaker"
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Test Speaker</span>
        </div>
        <div className="grid-cell left">
          <TestButton
            onClick={onTestSpeaker}
            isActive={isSpeakerTesting}
            activeText="Stop Test"
            inactiveText={getTestButtonText()}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Level:</span>
        </div>
        <div className="grid-cell center">
          <MicLevelIndicator
            level={speakerAudioLevel}
            isActive={isSpeakerTesting && speakerAudioLevel > 0}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Volume:</span>
        </div>
        <div className="grid-cell center">
          <VolumeControl
            value={outputVolume}
            onChange={onOutputVolumeChange}
          />
        </div>
      </div>
    </div>
  );
}

export default SpeakerSettings;
