import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { Dropdown } from "antd";
import TestButton from './TestButton';
import MicLevelIndicator from './MicLevelIndicator';
import VolumeControl from './VolumeControl';
import { ReactComponent as SpeakerIcon } from "../../Assets/speaker.svg";
import soundTestAudio from "../../Assets/soundtest.mp3";

/**
 * SpeakerSettings Component
 * Self-contained speaker configuration section for prejoin page
 * Uses native Web APIs since user hasn't joined room yet
 * @param {number} outputVolume - Current output volume (0-100)
 * @param {function} onOutputVolumeChange - Output volume change handler
 */
function SpeakerSettings({
  outputVolume = 100,
  onOutputVolumeChange
}) {
  // Local state for speaker devices and selection
  const [speakerDevices, setSpeakerDevices] = useState([]);
  const [selectedSpeakerId, setSelectedSpeakerId] = useState('');

  // Local state for speaker testing
  const [isSpeakerTesting, setIsSpeakerTesting] = useState(false);
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);
  const testAudioRef = useRef(null);
  const levelIntervalRef = useRef(null);

  // Fetch speaker devices on component mount
  useEffect(() => {
    const fetchSpeakerDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
        setSpeakerDevices(audioOutputs);

        // Set default speaker if none selected
        if (audioOutputs.length > 0 && !selectedSpeakerId) {
          setSelectedSpeakerId(audioOutputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error fetching speaker devices:', error);
      }
    };

    fetchSpeakerDevices();
  }, [selectedSpeakerId]);
  // Handle speaker device change
  const handleSpeakerDeviceChange = useCallback((deviceId) => {
    setSelectedSpeakerId(deviceId);
    console.log('🔄 Speaker device changed to:', deviceId);
  }, []);

  // Speaker dropdown content
  const speakerDropdownContent = useMemo(() => (
    <div className="speaker-device-dropdown-menu">
      {speakerDevices.map((device) => {
        const isSelected = selectedSpeakerId === device.deviceId;
        const deviceLabel = device.label || `Speaker ${device.deviceId.slice(0, 8)}...`;

        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleSpeakerDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [speakerDevices, selectedSpeakerId, handleSpeakerDeviceChange]);

  // Current device name for display
  const currentDeviceName = useMemo(() => {
    const currentDevice = speakerDevices.find(device => device.deviceId === selectedSpeakerId);
    return currentDevice?.label || 'Default Speaker';
  }, [speakerDevices, selectedSpeakerId]);

  // Test speaker functionality
  const testSpeaker = useCallback(async () => {
    if (isSpeakerTesting) {
      // Stop testing
      if (testAudioRef.current) {
        testAudioRef.current.pause();
        testAudioRef.current = null;
      }
      if (levelIntervalRef.current) {
        clearInterval(levelIntervalRef.current);
        levelIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      return;
    }

    // Start testing
    setIsSpeakerTesting(true);
    console.log('🔊 Starting speaker test with device:', selectedSpeakerId);

    try {
      // Create test audio element
      const audio = new Audio(soundTestAudio);
      testAudioRef.current = audio;
      audio.volume = outputVolume / 100;

      // Set the audio output device if supported
      if (selectedSpeakerId && selectedSpeakerId !== 'default' && audio.setSinkId) {
        try {
          await audio.setSinkId(selectedSpeakerId);
          console.log('✅ Successfully set speaker device to:', selectedSpeakerId);
        } catch (error) {
          console.warn('❌ Failed to set audio output device:', error);
        }
      }

      // Start level simulation
      levelIntervalRef.current = setInterval(() => {
        const level = Math.random() * 80 + 20; // Random level between 20-100
        setSpeakerAudioLevel(level);
      }, 100);

      // Play the audio
      await audio.play();

      // Stop simulation when audio ends
      audio.addEventListener('ended', () => {
        if (levelIntervalRef.current) {
          clearInterval(levelIntervalRef.current);
          levelIntervalRef.current = null;
        }
        setSpeakerAudioLevel(0);
        setIsSpeakerTesting(false);
        if (testAudioRef.current) {
          testAudioRef.current = null;
        }
      });

    } catch (error) {
      console.error('Error playing test sound:', error);
      // Cleanup on error
      if (levelIntervalRef.current) {
        clearInterval(levelIntervalRef.current);
        levelIntervalRef.current = null;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      if (testAudioRef.current) {
        testAudioRef.current = null;
      }
    }
  }, [isSpeakerTesting, selectedSpeakerId, outputVolume]);

  return (
    <div className="settings-section speaker-settings-section">
      <div className="grid-container speaker-grid">
        <div className="grid-cell left">
          <span className="setting-label">Speaker</span>
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Choose Speaker</span>
        </div>
        <div className="grid-cell center">
          <Dropdown
            dropdownRender={() => speakerDropdownContent}
            trigger={['click']}
            placement="top"
            disabled={speakerDevices.length === 0}
          >
            <button
              className="speaker-device-dropdown-button"
              type="button"
              aria-label={`Current speaker device: ${currentDeviceName}. Click to change device.`}
              aria-haspopup="listbox"
              aria-expanded="false"
            >
              <SpeakerIcon className="device-icon" />
              <span className="device-name">{currentDeviceName}</span>
            </button>
          </Dropdown>
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Test Speaker</span>
        </div>
        <div className="grid-cell left">
          <TestButton
            onClick={testSpeaker}
            isActive={isSpeakerTesting}
            activeText="Stop Test"
            inactiveText={isSpeakerTesting ? 'Stop Test' : 'Test Speaker'}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Level:</span>
        </div>
        <div className="grid-cell center">
          <MicLevelIndicator
            level={speakerAudioLevel}
            isActive={isSpeakerTesting && speakerAudioLevel > 0}
          />
        </div>

        <div className="grid-cell center">
          {/* Empty cell */}
        </div>
        <div className="grid-cell left">
          <span className="setting-sublabel">Output Volume:</span>
        </div>
        <div className="grid-cell center">
          <VolumeControl
            value={outputVolume}
            onChange={onOutputVolumeChange}
          />
        </div>
      </div>
    </div>
  );
}

export default SpeakerSettings;
